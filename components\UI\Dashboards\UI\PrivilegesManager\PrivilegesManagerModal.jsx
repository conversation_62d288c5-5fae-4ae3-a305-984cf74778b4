import styles from './PrivilegesManagerModal.module.css' // Import new module.css
import save from '../../../../../svg/White save_line.svg'
import Image from 'next/image'
import SelectInput from '../../../Input/SelectInput/SelectInput'
import Button from '../../../Button/Button'
import TextInput from '../../../Input/TextInput/TextInput'
import TextareaInput from '../../../Input/TextareaInput/TextareaInput'
import { useState, useEffect, useContext, use } from 'react'
import { useApi } from '../../../../../hooks/useApi'
import { useDashboard } from '../../../../../hooks/useDashboard'
import { AsyncMultiSelectDropdown } from '../../../Input/AsyncDropdown/AsyncMultiSelectDropdown'
import { PermissionsEnum, FormUserRoleEnum, UpdatePermissionDMSEnum, ModuleIdEnum } from '../../../../../utillites/enums' // Import PermissionsEnum and FormUserRoleEnum
import { ToastContext } from '../../../../../pages/_app'
import UserProfileContext from '../../../../../public/UserProfileContext/UserProfileContext'
import Info from '../../../../../svg/info.svg'
import { ConditionalDisplay } from '../../../ConditionalDisplay/ConditionalDisplay'

const PrivilegesManagerModal = ({ setIsModalVisible }) => {
  const { userID } = useContext(UserProfileContext)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    acceleratorId: 1,
    moduleId: 0,
    formDefinitionId: [],
    formUserRole: [],
    formApprovalStage: [],
    documentTypeId: [],
    documentId: [],
    permission: [],
    updateOptions: [1, 2, 3, 4],
    fileRoutings: [],
    type: 1,
    createdBy: userID,
    isActive: true
  })
  const [errors, setErrors] = useState({})
  const { callApi, loading } = useApi()
  const [acceleratorOptions, setAcceleratorOptions] = useState([])
  const [moduleOptions, setModuleOptions] = useState([])
  const [formDefinitionOptions, setFormDefinitionOptions] = useState([])
  const fileRoutingTypeOptions = [
    { label: 'Definitions', value: 1 },
    { label: 'Approvals', value: 2 }
  ]

  const [lazyParams, setLazyParams] = useState({ page: 0, rows: 10 })
  const {
    lazyParamsToQueryString,
    setTotalCount: setFileRoutingCount,
    rows: fileRoutingOptions,
    setRows: setFileRoutingOptions,
    lazyParams: fileRoutingLazyparams,
    onPage: onFileRoutingPage
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      page: 0,
      rows: 10,
      sortField: 'name',
      sortOrder: 1,
      filters: {
        global: { value: '', matchMode: 'contains' }
      }
    }
  })
  const [fileRoutingStageOptions, setFileRoutingStageOptions] = useState([])
  const [formDefinitionLoading, setFormDefinitionLoading] = useState(false)
  const [count, setCount] = useState(0) // State to hold the count of form definitions
  const [documentTypeOptions, setDocumentTypeOptions] = useState([])
  const [documentOptions, setDocumentOptions] = useState([])
  const toast = useContext(ToastContext)

  const formUserRoleOptions = Object.keys(FormUserRoleEnum).map((key) => ({
    label: key,
    value: FormUserRoleEnum[key]
  }))

  useEffect(() => {
    const fetchAcceleratorOptions = async () => {
      try {
        const response = await callApi({
          method: 'GET',
          url: '/Solution'
        })
        if (response?.data) {
          setAcceleratorOptions(response.data)
        }
      } catch (error) {
        console.error('Error fetching accelerator options:', error)
      }
    }

    fetchAcceleratorOptions()
  }, [])

  useEffect(() => {
    const fetchModuleOptions = async () => {
      if (!formData.acceleratorId) return // Skip if no Accelerator is selected
      try {
        const response = await callApi({
          method: 'GET',
          url: `GetModulesBySolutionId?solutionId=${formData.acceleratorId}`
        })
        if (response?.data) {
          setModuleOptions(response.data)
        }
      } catch (error) {
        console.error('Error fetching module options:', error)
      }
    }

    fetchModuleOptions()
  }, [formData.acceleratorId])

  useEffect(() => {
    const fetchFormDefinitionOptions = async () => {
      if (!formData.moduleId) return // Skip if no Module is selected
      const queryString = lazyParamsToQueryString(lazyParams) // Convert lazyParams to query string
      setFormDefinitionLoading(true)
      try {
        const response = await callApi({
          method: 'GET',
          url: `FormDefinition/filter${queryString}&solutionId=${formData.acceleratorId}`
        })
        if (response?.data) {
          setFormDefinitionOptions(
            response.data.formDefinitions.map((item) => ({
              id: item.formId,
              name: item.name
            }))
          )
          setCount(response.data.count) // Assuming count is part of the response
        }
      } catch (error) {
        setFormDefinitionOptions([])
        setCount(0)
        console.error('Error fetching form definition options:', error)
      } finally {
        setFormDefinitionLoading(false)
      }
    }

    fetchFormDefinitionOptions()
  }, [formData.moduleId, lazyParams])

  useEffect(() => {
    const fetchDocumentTypeOptions = async () => {
      if (formData.moduleId === ModuleIdEnum.EZDOCS) {
        try {
          const response = await callApi({
            method: 'GET',
            url: '/GetAllIOVValue/5' // Replace with actual API endpoint
          })
          if (response?.data) {
            setDocumentTypeOptions(
              response.data.map((item) => ({
                value: item.id,
                label: item.name,
                path: [item.path.departmentName, item.path.subDepartmentName, item.path.teamName].filter((item) => item).join(' > '),
                parentValueId: item.parentValueId
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching document type options:', error)
        }
      }
    }

    fetchDocumentTypeOptions()
  }, [formData.moduleId])

  useEffect(() => {
    const fetchDocumentOptions = async () => {
      if (formData.moduleId === ModuleIdEnum.EZDOCS) {
        try {
          const response = await callApi({
            method: 'GET',
            url: '/GetAllIOVValue/6' // Replace with actual API endpoint
          })
          if (response?.data) {
            setDocumentOptions(
              response.data.map((item) => ({
                value: item.id,
                label: item.name,
                path: [item.path.departmentName, item.path.subDepartmentName, item.path.teamName, item.path.documentTypeName]
                  .filter((item) => item)
                  .join(' > '),
                parentValueId: item.parentValueId
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching document type options:', error)
        }
      }
    }

    fetchDocumentOptions()
  }, [formData.moduleId])

  useEffect(() => {
    const fetchFileRouting = async () => {
      if (formData.moduleId === ModuleIdEnum.EZROUTING) {
        const queryString = lazyParamsToQueryString(fileRoutingLazyparams)
        try {
          const response = await callApi({
            method: 'GET',
            url: `FileRouting${queryString}`
          })
          if (response?.data) {
            // Assuming the API returns data in a similar format to other endpoints
            setFileRoutingOptions(
              response.data.rows.map((item) => ({
                id: item.id,
                name: item.name
              }))
            )
            // Set count if available in response
            setFileRoutingCount(response.data.count)
          }
        } catch (error) {
          setFileRoutingOptions([])
          setFileRoutingCount(0)
          console.error('Error fetching file routing options:', error)
        }
      } else {
        // Reset when module is not EZROUTING
        setFileRoutingOptions([])
        setFileRoutingCount(0)
      }
    }
    fetchFileRouting()
  }, [formData.moduleId, fileRoutingLazyparams])

  useEffect(() => {
    const getListOfStageName = async () => {
      if (formData.moduleId === ModuleIdEnum.EZROUTING) {
        const queryString = formData?.formDefinitionId?.map((id) => `dmsDefinitionIds=${id}`).join('&')
        try {
          const response = await callApi({
            method: 'GET',
            url: `PrivilegeManager/GetListofStageName?${queryString}` // Replace with actual API endpoint
          })
          if (response?.data) {
            setFileRoutingStageOptions(
              response.data.map((item) => ({
                id: item.stageId,
                name: item.stageName
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching stage name options:', error)
        }
      }
    }
    getListOfStageName()
  }, [formData.moduleId, formData.formDefinitionId])

  const saveIcon = () => {
    return <Image src={save} alt="save-icon" />
  }

  const onChangeHandler = (e) => {
    const { name, value } = e.target

    setFormData((prev) => {
      let data = {
        ...prev,
        [name]: value,
        moduleId: name === 'acceleratorId' ? undefined : name === 'moduleId' ? value : prev.moduleId // Reset Module if Accelerator changes
      }

      if (name === 'documentId') {
        const documentTypeIds = []
        for (const doc of documentOptions) {
          if (value.includes(doc.value)) {
            if (doc.parentValueId && !!documentTypeOptions.find((docType) => docType?.value === doc?.parentValueId))
              documentTypeIds.push(doc.parentValueId)
          }
        }
        console.log('documentTypeIds', documentTypeIds)
        data.documentTypeId = documentTypeIds
      }

      return data
    })

    // Validation logic
    const newErrors = { ...errors }
    if (!value) {
      newErrors[name] = `${name} is required`
    } else {
      delete newErrors[name]
    }
    setErrors(newErrors)
  }

  const savePrivilege = async () => {
    // Enhanced validation for all required fields
    const validationErrors = {}
    if (!formData.name) validationErrors.name = 'Name is required'
    if (!formData.description) validationErrors.description = 'Description is required'
    if (!formData.acceleratorId) validationErrors.acceleratorId = 'Accelerator is required'
    if (!formData.moduleId) validationErrors.moduleId = 'Module is required'

    if (formData.moduleId === ModuleIdEnum.EZDOCS) {
      if (!formData.permission || formData.permission.length === 0) {
        validationErrors.permission = 'Permission is required'
      }
      if (formData.permission.includes(PermissionsEnum.Update)) {
        if (!formData.updateOptions || formData.updateOptions.length === 0) {
          validationErrors.updateOptions = 'Update Permission is required'
        }
      }
    } else if (formData.moduleId === ModuleIdEnum.EZROUTING) {
      if (!formData.formDefinitionId || formData.formDefinitionId.length === 0) {
        validationErrors.formDefinitionId = 'File Routing Definition is required'
      }
    } else {
      if (!formData.formDefinitionId || formData.formDefinitionId.length === 0) {
        validationErrors.formDefinitionId = 'Form Definition is required'
      }
      if (!formData.formUserRole || formData.formUserRole.length === 0) {
        validationErrors.formUserRole = 'Form User Role is required'
      }
      if (
        formData.formDefinitionId &&
        formData.formDefinitionId.length <= 1 &&
        (!formData.formApprovalStage || formData.formApprovalStage.length === 0)
      ) {
        validationErrors.formApprovalStage = 'Form Approval Stage is required'
      }
    }

    if (Object.keys(validationErrors).length > 0) {
      console.log('validationErrors', validationErrors)
      setErrors(validationErrors)
      toast.current.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please correct the errors before proceeding.'
      })
      return
    }
    const postData = {
      ...formData,
      updateOptions: formData.permission.includes(PermissionsEnum.Update) ? formData.updateOptions : []
      // formDefinitionId: [...formData?.formDefinitionId?.map((item) => item.id)]
    }
    // if (formData.formDefinitionId.length === 0) {
    //   postData.formDefinitionId = formData.formDefinitionId.map(
    //     (item) => item.id
    //   );
    // }
    try {
      const response = await callApi({
        method: 'POST',
        url: 'PrivilegeManager',
        data: postData
      })
      if (response?.data?.status === false) {
        toast.current.show({
          severity: 'warn',
          summary: 'Error',
          detail: response?.data?.message || 'Failed to create privilege.'
        })
      } else {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Privilege created successfully!'
        })
        setIsModalVisible(false)
        setFormData({})
        setErrors({})
      }
    } catch (error) {
      console.error('Error saving privilege:', error)
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An unexpected error occurred.'
      })
    }
  }

  const folderStructureItemTemplate = (option) => {
    return (
      <div className="flex items-center justify-between w-full px-2">
        <div className="text-sm text-wrap flex-1">{option.label}</div>
        <Image className="ml-2 mt-1 cursor-pointer" title={option.path} alt="info" src={Info} style={{ minWidth: '16px' }} />
      </div>
    )
  }

  return (
    <div className={styles.modalContainer}>
      <div className={styles.field}>
        <TextInput label="Name" name="name" value={formData.name || ''} onChange={onChangeHandler} error={errors.name} required />
      </div>
      <div className={styles.field}>
        <TextareaInput
          label="Description"
          name="description"
          value={formData.description || ''}
          onChange={onChangeHandler}
          error={errors.description}
          required
          rows={5}
          cols={30}
          maxLength={200}
          height="5rem"
          containerHeight="auto"
          message="Note: Maximum 200 characters"
        />
      </div>
      <div className={styles.twoColumnLayout}>
        <div className={styles.field}>
          <SelectInput
            label="Accelerator"
            name="acceleratorId"
            options={acceleratorOptions}
            optionLabel="name"
            optionValue="id"
            value={formData.acceleratorId || ''}
            onChange={onChangeHandler}
            error={errors.acceleratorId}
            required
          />
        </div>
        <div className={styles.field}>
          <SelectInput
            label="Module"
            name="moduleId"
            options={moduleOptions}
            optionLabel="moduleName"
            optionValue="id"
            value={formData.moduleId || ''}
            onChange={onChangeHandler}
            error={errors.moduleId}
            required
            disabled={!formData.acceleratorId} // Disable if Accelerator is undefined
          />
        </div>
        <ConditionalDisplay condition={formData && formData.moduleId === ModuleIdEnum.EZDOCS}>
          <>
            <div className={styles.field}>
              <SelectInput
                label="Document Type"
                name="documentTypeId"
                options={documentTypeOptions}
                value={formData.documentTypeId || ''}
                onChange={onChangeHandler}
                error={errors.documentTypeId}
                multiple
                parentClassName="w-27rem"
                filter
                filterBy="label"
                itemTemplate={folderStructureItemTemplate}
              />
            </div>
            <div className={styles.field}>
              <SelectInput
                label="Document"
                name="documentId"
                options={documentOptions}
                value={formData.documentId || ''}
                onChange={onChangeHandler}
                error={errors.documentId}
                multiple
                parentClassName="w-27rem"
                filter
                filterBy="label"
                itemTemplate={folderStructureItemTemplate}
              />
            </div>
            <div className={styles.field}>
              <SelectInput
                label="Permission"
                name="permission"
                options={Object.keys(PermissionsEnum).map((key) => ({
                  label: key,
                  value: PermissionsEnum[key]
                }))}
                value={formData.permission || ''}
                onChange={onChangeHandler}
                error={errors.permission}
                required
                multiple={true}
              />
            </div>
            <ConditionalDisplay condition={formData?.permission?.includes(PermissionsEnum.Update)}>
              <div className={styles.field}>
                <SelectInput
                  label="Update Options"
                  name="updateOptions"
                  options={Object.keys(UpdatePermissionDMSEnum).map((key) => ({
                    label: key,
                    value: UpdatePermissionDMSEnum[key]
                  }))}
                  value={formData.updateOptions || ''}
                  onChange={onChangeHandler}
                  error={errors.updateOptions}
                  required
                  multiple={true}
                />
              </div>
            </ConditionalDisplay>
          </>
        </ConditionalDisplay>
        <ConditionalDisplay condition={formData && formData.moduleId === ModuleIdEnum.EZFORMS}>
          <div className={styles.field}>
            <AsyncMultiSelectDropdown
              label={'Form Definition'}
              name="formDefinitionId"
              required
              lazyParams={lazyParams}
              onFocus={() => setLazyParams({ ...lazyParams, page: 0 })}
              onTextChange={(e) => {}}
              showOptions={!!formData.moduleId}
              selectedOptions={formData.formDefinitionId}
              options={formDefinitionOptions}
              onSelect={(option) => {
                setFormData((prev) => ({
                  ...prev,
                  formDefinitionId: option
                }))
              }}
              loading={formDefinitionLoading}
              labelName="name"
              totalCount={count}
              onPageChange={(e) => setLazyParams({ ...lazyParams, page: e.page })}
              globalFilter={
                formData.formDefinitionId
                  ?.reduce((prev, curr) => {
                    return prev + curr.name + ','
                  }, '')
                  ?.slice(0, -2) || ''
              }
            />
          </div>
          <div className={styles.field}>
            <SelectInput
              label="Form User Role"
              name="formUserRole"
              options={formUserRoleOptions}
              value={formData.formUserRole || ''}
              onChange={onChangeHandler}
              error={errors.formUserRole}
              required
              multiple={true}
            />
          </div>
          <ConditionalDisplay condition={formData.formDefinitionId && formData.formDefinitionId.length <= 1}>
            <div className={styles.field}>
              <SelectInput
                label="Form Approval Stage"
                name="formApprovalStage"
                options={['Approval Stage 1', 'Approval Stage 2']}
                value={formData.formApprovalStage || ''}
                onChange={onChangeHandler}
                error={errors.formApprovalStage}
                required
                multiple={true}
                height="50px"
              />
            </div>
          </ConditionalDisplay>
        </ConditionalDisplay>
        <ConditionalDisplay condition={formData && formData.moduleId === ModuleIdEnum.EZROUTING}>
          <SelectInput
            label="Type"
            name="type"
            options={fileRoutingTypeOptions}
            value={formData.type || ''}
            onChange={onChangeHandler}
            error={errors.type}
            required
          />

          <SelectInput
            label="Definition"
            name="formDefinitionId"
            options={fileRoutingOptions}
            optionLabel="name"
            optionValue="id"
            value={formData.formDefinitionId || []}
            onChange={onChangeHandler}
            error={errors.fileDefinition}
            required
            multiple={true}
            loading={loading}
            filter
            filterBy="name"
            placeholder="Select routing definitions"
            virtualScrollerOptions={{
              lazy: true,
              onLazyLoad: (event) => {
                onFileRoutingPage({
                  ...fileRoutingLazyparams,
                  first: event.first,
                  page: Math.floor(event.first / fileRoutingLazyparams.rows),
                  filters: {
                    global: { value: event.filter || '', matchMode: 'contains' }
                  }
                })
              },
              itemSize: 38,
              showLoader: true,
              loading: loading,
              delay: 250
            }}
          />

          <SelectInput
            label="Stage"
            name="formApprovalStage"
            optionLabel="name"
            optionValue="id"
            placeholder="Select stage"
            options={fileRoutingStageOptions}
            value={formData.formApprovalStage || ''}
            onChange={onChangeHandler}
            error={errors.formApprovalStage}
            multiple={true}
            required
          />
          <div className={styles.field}>
            <SelectInput
              label="Permission"
              name="permission"
              options={Object.keys(PermissionsEnum).map((key) => ({
                label: key,
                value: PermissionsEnum[key]
              }))}
              value={formData.permission || ''}
              onChange={onChangeHandler}
              error={errors.permission}
              required
              multiple={true}
            />
          </div>
        </ConditionalDisplay>
      </div>
      <div className={styles.buttonContainer}>
        <Button
          label="Save"
          icon={saveIcon}
          onClick={savePrivilege}
          disabled={loading}
          loading={loading?.['PrivilegeManager']}
          width="150px"
        />
      </div>
    </div>
  )
}
export default PrivilegesManagerModal
