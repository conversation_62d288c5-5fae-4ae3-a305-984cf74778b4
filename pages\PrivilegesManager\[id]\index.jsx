import { useRouter } from 'next/router'
import { useState, useEffect, useContext } from 'react'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import TextareaInput from '../../../components/UI/Input/TextareaInput/TextareaInput'
import CheckBox from '../../../components/UI/Input/CheckBox/CheckBox'
import SelectInput from '../../../components/UI/Input/SelectInput/SelectInput'
import Button from '../../../components/UI/Button/Button'
import { useApi } from '../../../hooks/useApi'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import styles from '../PrivilegesManager.module.css'
import Image from 'next/image'
import { PermissionsEnum, FormUserRoleEnum, UpdatePermissionDMSEnum, ModuleIdEnum } from '../../../utillites/enums' // Import PermissionsEnum and FormUserRoleEnum
import { AsyncMultiSelectDropdown } from '../../../components/UI/Input/AsyncDropdown/AsyncMultiSelectDropdown'
import { ToastContext } from '../../../pages/_app'
import useUtilityFunctions from '../../../hooks/useUtilityFunctions'
import Backarrow from '../../../svg/backarrow.svg'
import clsx from 'clsx'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import { useDashboard } from '../../../hooks/useDashboard'
import Info from '../../../svg/info.svg'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { LoadingScreen } from '../../../components/UI/LoadingScreen/LoadingScreen'
import { Card } from 'primereact/card'
import SaveIcon from '../../../svg/White save_line.svg'

const fileRoutingTypeOptions = [
  { label: 'Definitions', value: 1 },
  { label: 'Approvals', value: 2 }
]

const PrivilegesManager = ({ id }) => {
  const router = useRouter()
  const { callApi, loading, error } = useApi()
  const toast = useContext(ToastContext)
  const { userID } = useContext(UserProfileContext) // Assuming userID is available in the context

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams: historyLazyParams,
    onSort,
    onPage,
    lazyParamsToQueryString
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      rows: 10,
      page: 0,
      sortField: 'createDate',
      sortOrder: -1,
      filters: {
        global: { value: '', matchMode: 'contains' }
      }
    }
  })

  const {
    setTotalCount: setFileRoutingCount,
    rows: fileRoutingOptions,
    setRows: setFileRoutingOptions,
    lazyParams: fileRoutingLazyparams,
    onPage: onFileRoutingPage
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      page: 0,
      rows: 10,
      sortField: 'name',
      sortOrder: 1,
      filters: {
        global: { value: '', matchMode: 'contains' }
      }
    }
  })
  const [fileRoutingStageOptions, setFileRoutingStageOptions] = useState([])

  const defaultPrivilege = {
    id: 0,
    name: 'string',
    description: 'string',
    acceleratorId: 0,
    moduleId: 0,
    formDefinitionId: [0],
    formUserRole: [0],
    formApprovalStage: [],
    documentTypeId: [0],
    documentId: [0],
    permission: [], // Ensure permission is an array
    updateOptions: [1, 2, 3, 4],
    updatedBy: 0,
    isActive: true
  }
  const [privilege, setPrivilege] = useState(defaultPrivilege)
  const [isEditing, setIsEditing] = useState(false)
  const [acceleratorOptions, setAcceleratorOptions] = useState([])
  const [moduleOptions, setModuleOptions] = useState([])
  const [documentOptions, setDocumentOptions] = useState([])
  const [documentRoutingStageOptions, setDocumentRoutingStageOptions] = useState([])
  const [documentTypeOptions, setDocumentTypeOptions] = useState([])
  const [formDefinitionOptions, setFormDefinitionOptions] = useState([])
  const [formDefinitionLoading, setFormDefinitionLoading] = useState(false)
  const [lazyParams, setLazyParams] = useState({
    page: 0,
    rows: 10,
    sortOrder: -1
  })
  const [formDefinitionCount, setFormDefinitionCount] = useState(0)

  const formUserRoleOptions = Object.keys(FormUserRoleEnum).map((key) => ({
    label: key,
    value: FormUserRoleEnum[key]
  }))

  const { createUserFriendlyDate } = useUtilityFunctions()

  useEffect(() => {
    const fetchPrivilege = async () => {
      console.log('id', id)
      if (id) {
        console.log('id1', id)
        try {
          const data = await callApi({
            url: `PrivilegeManager/${id}`,
            method: 'GET'
          })
          data.data.permission = data.data.permission || [] // Ensure permission is an array
          setPrivilege(data.data)
        } catch (err) {
          setPrivilege(defaultPrivilege) // Fallback to default if error occurs
          console.error(err)
        }
      }
    }

    fetchPrivilege()
  }, [])

  useEffect(() => {
    const fetchAcceleratorOptions = async () => {
      try {
        const response = await callApi({
          method: 'GET',
          url: 'Solution'
        })
        if (response?.data) {
          setAcceleratorOptions(response.data.map((item) => ({ value: item.id, label: item.name })))
        }
      } catch (error) {
        console.error('Error fetching accelerator options:', error)
      }
    }

    fetchAcceleratorOptions()
  }, [])

  useEffect(() => {
    const fetchModuleOptions = async () => {
      if (!privilege.acceleratorId) return // Skip if no accelerator is selected
      try {
        const response = await callApi({
          method: 'GET',
          url: `GetModulesBySolutionId?solutionId=${privilege.acceleratorId}`
        })
        if (response?.data) {
          setModuleOptions(
            response.data.map((item) => ({
              value: item.id,
              label: item.moduleName
            }))
          )
        }
      } catch (error) {
        console.error('Error fetching module options:', error)
      }
    }

    fetchModuleOptions()
  }, [privilege?.acceleratorId])

  useEffect(() => {
    const fetchDocumentRoutingStageOptions = async () => {
      if (privilege.moduleId === ModuleIdEnum.EZDOCS && privilege.documentId) {
        try {
          const response = await callApi({
            method: 'POST',
            url: 'GetAllChildDepartment',
            data: privilege.documentId
          })
          if (response?.data) {
            setDocumentRoutingStageOptions(
              response.data.map((item) => ({
                value: item.id,
                label: item.name
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching document routing stage options:', error)
        }
      }
    }

    fetchDocumentRoutingStageOptions()
  }, [privilege?.documentId])

  useEffect(() => {
    const fetchDocumentTypeOptions = async () => {
      if (privilege.moduleId === ModuleIdEnum.EZDOCS) {
        try {
          const response = await callApi({
            method: 'GET',
            url: '/GetAllIOVValue/5' // Fetch document type options
          })
          if (response?.data) {
            setDocumentTypeOptions(
              response.data.map((item) => ({
                value: item.id,
                label: item.name,
                path: [item.path.departmentName, item.path.subDepartmentName, item.path.teamName].filter((item) => item).join(' > '),
                parentValueId: item.parentValueId
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching document type options:', error)
        }
      }
    }

    fetchDocumentTypeOptions()
  }, [privilege?.moduleId])

  useEffect(() => {
    const fetchDocumentOptions = async () => {
      if (privilege.moduleId === ModuleIdEnum.EZDOCS) {
        try {
          const response = await callApi({
            method: 'GET',
            url: '/GetAllIOVValue/6' // Fetch document options
          })
          if (response?.data) {
            setDocumentOptions(
              response.data.map((item) => ({
                value: item.id,
                label: item.name,
                path: [item.path.departmentName, item.path.subDepartmentName, item.path.teamName, item.path.documentTypeName]
                  .filter((item) => item)
                  .join(' > '),
                parentValueId: item.parentValueId
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching document options:', error)
        }
      }
    }

    fetchDocumentOptions()
  }, [privilege.moduleId])

  useEffect(() => {
    const fetchFormDefinitionOptions = async () => {
      if (!privilege.moduleId) return // Skip if no Module is selected
      setFormDefinitionLoading(true)
      try {
        const response = await callApi({
          method: 'GET',
          url: `FormDefinition/Filter?page=${lazyParams.page}&rows=${lazyParams.rows}&sortOrder=${lazyParams.sortOrder}&solutionId=${privilege.acceleratorId}`
        })
        if (response?.data) {
          setFormDefinitionOptions(
            response.data.formDefinitions.map((item) => ({
              id: item.formId,
              name: item.name
            }))
          )
          setFormDefinitionCount(response.data.count)
        }
      } catch (error) {
        setFormDefinitionOptions([])
        setFormDefinitionCount(0)
        console.error('Error fetching form definition options:', error)
      } finally {
        setFormDefinitionLoading(false)
      }
    }

    fetchFormDefinitionOptions()
  }, [privilege.moduleId, lazyParams])

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const queryString = lazyParamsToQueryString(historyLazyParams)
        const response = await callApi({
          method: 'GET',
          url: `PrivilegeManagerHistory/Filter${queryString}&privilegeManagerId=${id}`
        })
        if (response?.data) {
          setRows(response.data.privilegeManagerHistoryDTO)
          setTotalCount(response.data.count)
        }
      } catch (error) {
        console.error('Error fetching history:', error)
      }
    }

    if (id) fetchHistory()
  }, [id, isEditing])

  useEffect(() => {
    const fetchFileRouting = async () => {
      if (privilege.moduleId === ModuleIdEnum.EZROUTING) {
        const queryString = lazyParamsToQueryString(fileRoutingLazyparams)
        try {
          const response = await callApi({
            method: 'GET',
            url: `FileRouting${queryString}`
          })
          if (response?.data) {
            // Assuming the API returns data in a similar format to other endpoints
            setFileRoutingOptions(
              response.data.rows.map((item) => ({
                id: item.id,
                name: item.name
              }))
            )
            // Set count if available in response
            setFileRoutingCount(response.data.count)
          }
        } catch (error) {
          setFileRoutingOptions([])
          setFileRoutingCount(0)
          console.error('Error fetching file routing options:', error)
        }
      } else {
        // Reset when module is not EZROUTING
        setFileRoutingOptions([])
        setFileRoutingCount(0)
      }
    }
    fetchFileRouting()
  }, [id, fileRoutingLazyparams])

  useEffect(() => {
    const getListOfStageName = async () => {
      if (privilege.moduleId === ModuleIdEnum.EZROUTING) {
        const queryString = privilege?.formDefinitionId?.map((id) => `dmsDefinitionIds=${id}`).join('&')
        try {
          const response = await callApi({
            method: 'GET',
            url: `PrivilegeManager/GetListofStageName?${queryString}` // Replace with actual API endpoint
          })
          if (response?.data) {
            setFileRoutingStageOptions(
              response.data.map((item) => ({
                id: item.stageId,
                name: item.stageName
              }))
            )
          }
        } catch (error) {
          console.error('Error fetching stage name options:', error)
        }
      }
    }
    getListOfStageName()
  }, [id, privilege.formDefinitionId])

  const handleEditToggle = () => {
    setIsEditing(!isEditing)
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    const keys = name.split('.')
    let updatedValue = type === 'checkbox' ? checked : value

    console.log('updatedValue', updatedValue)
    setPrivilege((prevPrivilege) => {
      const updatedPrivilege = { ...prevPrivilege }

      // Handle permissions logic
      if (keys[0] === 'permission') {
        const permissionValue = parseInt(keys[1], 10)
        const currentPermissions = Array.isArray(prevPrivilege.permission) ? prevPrivilege.permission : [] // Ensure it's an array
        updatedPrivilege.permission = checked
          ? [...currentPermissions, permissionValue]
          : currentPermissions.filter((perm) => perm !== permissionValue)
      } else if (keys[0] === 'updateOptions') {
        const permissionValue = parseInt(keys[1], 10)
        const currentPermissions = Array.isArray(prevPrivilege.updateOptions) ? prevPrivilege.updateOptions : [] // Ensure it's an array
        updatedPrivilege.updateOptions = checked
          ? [...currentPermissions, permissionValue]
          : currentPermissions.filter((perm) => perm !== permissionValue)
      } else {
        // Handle other fields
        let current = updatedPrivilege
        for (let i = 0; i < keys.length - 1; i++) {
          current[keys[i]] = { ...current[keys[i]] }
          current = current[keys[i]]
        }
        current[keys[keys.length - 1]] = updatedValue
      }

      // Reset module if accelerator changes
      if (name === 'acceleratorId') {
        updatedPrivilege.moduleId = undefined
      }

      // Update document type if document changes
      if (name === 'documentId') {
        const documentTypeIds = []
        for (const doc of documentOptions) {
          if (updatedValue.includes(doc.value)) {
            if (doc.parentValueId && !!documentTypeOptions.find((docType) => docType?.value === doc?.parentValueId))
              documentTypeIds.push(doc.parentValueId)
          }
        }
        console.log('documentTypeIds', documentTypeIds)
        updatedPrivilege.documentTypeId = documentTypeIds
      }

      return updatedPrivilege
    })
  }

  const handleSave = async () => {
    try {
      const response = await callApi({
        url: `UpdatePrivilegeManager`,
        method: 'PATCH',
        data: {
          id: privilege.id,
          name: privilege.name,
          description: privilege.description,
          acceleratorId: privilege.acceleratorId,
          moduleId: privilege.moduleId,
          formDefinitionId: privilege.formDefinitionId,
          formUserRole: privilege.formUserRole,
          formApprovalStage: privilege.formApprovalStage,
          documentTypeId: privilege.documentTypeId,
          documentId: privilege.documentId,
          permission: privilege.permission,
          updateOptions: privilege.permission.includes(PermissionsEnum.Update) ? privilege.updateOptions : null,
          updatedBy: userID,
          isActive: privilege.isActive
        }
      })
      if (response?.data?.status === false) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: response?.data?.message || 'Failed to update privilege.'
        })
      } else {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Privilege updated successfully!'
        })
      }
      setIsEditing(false)
    } catch (err) {
      console.error(err)
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An unexpected error occurred.'
      })
    }
  }

  const handleBack = () => {
    router.push('/PrivilegesManager')
  }

  if (loading[`PrivilegeManager/${id}`]) return <LoadingScreen />
  if (error) return <div>Error: {error.message || 'An error occurred'}</div>
  if (!privilege) return <div>Loading...</div>

  const historyColumns = [
    {
      field: 'action',
      header: 'Action',
      body: (rowData) => (rowData.action === 1 ? 'Created' : rowData.action === 2 ? 'Updated' : 'Deleted')
    },
    { field: 'createdUser', header: 'User' },
    {
      field: 'createDate',
      header: 'Date',
      body: (rowData) => createUserFriendlyDate(rowData.createDate)
    }
  ]

  const folderStructureItemTemplate = (option) => {
    return (
      <div className="flex items-center justify-between w-full px-2">
        <div className="text-sm text-wrap flex-1">{option.label}</div>
        <Image className="ml-2 mt-1 cursor-pointer" title={option.path} alt="info" src={Info} style={{ minWidth: '16px' }} />
      </div>
    )
  }

  // if (loading?.[`PrivilegeManager/${id}`]) return ;

  return (
    <PageContainer>
      <div className="flex justify-content-between">
        <div className="flex gap-3 w-4">
          <Image src={Backarrow} onClick={handleBack} className="cursor-pointer" />
          <BreadCrumbs title="Privileges Manager" breadcrumbItems={[{ label: 'Settings' }, { label: 'Privileges Manager' }]} />
        </div>
        <div className="flex gap-3">
          <Button
            onClick={handleEditToggle}
            label={isEditing ? 'Cancel' : 'Edit'}
            icon={<i className={`pi ${isEditing ? 'pi-ban' : 'pi-pencil'}`} />}
            variant={isEditing ? 'outline' : 'fill'}
            width="150px"
          />
          {isEditing && <Button onClick={handleSave} icon={<Image src={SaveIcon} />} label="Save" width="150px" />}
        </div>
      </div>
      <div className={styles.container}>
        <Card className={clsx('w-full m-0', styles.documentCard)}>
          <div className={styles.headerContainer}>{privilege.name + ' - ' + privilege.id}</div>
          <div className={clsx('flex flex-column gap-3 mt-3 mb-5', styles.formSpace)}>
            <div className="flex gap-3">
              <TextInput label="Privilege ID" value={`PR-${privilege?.id ?? 0}`} readOnly />
              <TextInput label="Name" name="name" value={privilege.name} onChange={handleInputChange} readOnly={!isEditing} />
            </div>
            <div className="flex gap-3">
              <TextareaInput
                label="Description"
                name="description"
                value={privilege.description}
                onChange={handleInputChange}
                readOnly={!isEditing}
                rows={5}
                cols={30}
                maxLength={200}
                height="5rem"
                containerHeight="auto"
                message="Note: Maximum 200 characters"
                style={{ resize: 'none' }}
              />
              <CheckBox label="Status" name="isActive" checked={privilege.isActive} onChange={handleInputChange} disabled={!isEditing} />
            </div>
            <div className="flex gap-3">
              <SelectInput
                label="Accelerator"
                name="acceleratorId"
                value={privilege.acceleratorId}
                onChange={handleInputChange}
                disabled={!isEditing}
                options={acceleratorOptions}
                loading={loading?.['Solution']} // Show loading if accelerator options are not loaded
                filter
                filterBy="name"
                height="3.125rem"
              />
              <SelectInput
                label="Module"
                name="moduleId"
                value={privilege.moduleId}
                onChange={handleInputChange}
                disabled={!isEditing || !privilege.acceleratorId} // Disable if no accelerator is selected
                loading={loading?.[`GetModulesBySolutionId?solutionId=${privilege.acceleratorId}`]} // Show loading if accelerator options are not loaded
                options={moduleOptions}
                optionValue="value"
                optionLabel="label"
                filter
                filterBy="name"
                height="3.125rem"
              />
            </div>

            <ConditionalDisplay condition={privilege.moduleId === ModuleIdEnum.EZDOCS}>
              <>
                <div className="flex gap-3 w-full">
                  <SelectInput
                    label="Document Type"
                    name="documentTypeId"
                    value={privilege.documentTypeId}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    options={documentTypeOptions}
                    optionValue="value"
                    optionLabel="label"
                    multiple={true}
                    loading={loading?.['/GetAllIOVValue/5']}
                    parentClassName="w-full"
                    filter
                    filterBy="label"
                    itemTemplate={folderStructureItemTemplate}
                  />
                  <SelectInput
                    label="Document"
                    name="documentId"
                    value={privilege.documentId}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    options={documentOptions}
                    optionValue="value"
                    optionLabel="label"
                    multiple={true}
                    loading={loading?.['GetAllChildDepartment']} // Show loading if document options are not loaded
                    parentClassName="w-full"
                    filter
                    filterBy="label"
                    itemTemplate={folderStructureItemTemplate}
                  />
                </div>
                <div className="flex-nowrap flex-column w-6 gap-3">
                  <label className={styles.label}>Permissions</label>
                  <div className={clsx('flex-nowrap', styles.permissions)}>
                    {Object.entries(PermissionsEnum).map(([label, value]) => (
                      <CheckBox
                        key={value}
                        label={label}
                        name={`permission.${value}`}
                        checked={Array.isArray(privilege.permission) && privilege.permission.includes(value)} // Ensure privilege.permission is an array
                        onChange={handleInputChange}
                        disabled={!isEditing}
                      />
                    ))}
                  </div>
                </div>

                <ConditionalDisplay condition={privilege.permission.includes(PermissionsEnum.Update)}>
                  <div className={clsx('flex-nowrap flex-column w-6', styles.inputGroup)}>
                    <label className={styles.label}>Update Options</label>
                    <div className={clsx('flex-nowrap', styles.permissions)}>
                      {Object.entries(UpdatePermissionDMSEnum).map(([label, value]) => (
                        <CheckBox
                          key={value}
                          label={label}
                          name={`updateOptions.${value}`}
                          checked={Array.isArray(privilege.updateOptions) && privilege.updateOptions.includes(value)} // Ensure privilege.permission is an array
                          onChange={handleInputChange}
                          disabled={!isEditing}
                        />
                      ))}
                    </div>
                  </div>
                </ConditionalDisplay>
              </>
            </ConditionalDisplay>
            <ConditionalDisplay condition={privilege.moduleId === ModuleIdEnum.EZROUTING}>
              <SelectInput
                label="Type"
                name="type"
                options={fileRoutingTypeOptions}
                value={privilege.type || ''}
                onChange={on}
                error={error.type}
                required
              />

              <SelectInput
                label="Definition"
                name="formDefinitionId"
                options={fileRoutingOptions}
                optionLabel="name"
                optionValue="id"
                value={privilege.formDefinitionId || []}
                onChange={handleInputChange}
                error={error.fileDefinition}
                required
                multiple={true}
                loading={loading}
                filter
                filterBy="name"
                placeholder="Select routing definitions"
                virtualScrollerOptions={{
                  lazy: true,
                  onLazyLoad: (event) => {
                    onFileRoutingPage({
                      ...fileRoutingLazyparams,
                      first: event.first,
                      page: Math.floor(event.first / fileRoutingLazyparams.rows),
                      filters: {
                        global: { value: event.filter || '', matchMode: 'contains' }
                      }
                    })
                  },
                  itemSize: 38,
                  showLoader: true,
                  loading: loading,
                  delay: 250
                }}
              />

              <SelectInput
                label="Stage"
                name="formApprovalStage"
                optionLabel="name"
                optionValue="id"
                placeholder="Select stage"
                options={fileRoutingStageOptions}
                value={privilege.formApprovalStage || ''}
                onChange={handleInputChange}
                error={error.formApprovalStage}
                multiple={true}
                required
              />
              <div className={styles.field}>
                <SelectInput
                  label="Permission"
                  name="permission"
                  options={Object.keys(PermissionsEnum).map((key) => ({
                    label: key,
                    value: PermissionsEnum[key]
                  }))}
                  value={privilege.permission || ''}
                  onChange={handleInputChange}
                  error={error.permission}
                  required
                  multiple={true}
                />
              </div>
            </ConditionalDisplay>

            <ConditionalDisplay condition={privilege.moduleId === ModuleIdEnum.EZFORMS}>
              <>
                <div className={styles.inputGroup}>
                  <AsyncMultiSelectDropdown
                    label="Form Definition ID"
                    name="formDefinitionId"
                    lazyParams={lazyParams}
                    onFocus={() => setLazyParams({ ...lazyParams, page: 0 })}
                    onTextChange={(e) => {}}
                    showOptions={!!privilege.moduleId}
                    // selectedOptions={
                    //   privilege.formDefinitionId &&
                    //   typeof privilege.formDefinitionId[0] === "number"
                    //     ? formDefinitionOptions.map(
                    //         (item) =>
                    //           privilege.formDefinitionId.includes(item.id)
                    //             ? item
                    //             : { id: item.id, name: "..." } // Fallback to empty object if not found
                    //       )
                    //     : privilege.formDefinitionId
                    // }
                    selectedOptions={Object.entries(privilege?.formDefinitionList || {}).map(([id, name]) => ({
                      id: parseInt(id, 10),
                      name
                    }))}
                    options={formDefinitionOptions}
                    onSelect={(option) => {
                      const result = {}

                      option.forEach(({ id, name }) => {
                        if (!result[id]) {
                          const trimmedName = name.split(' - ')[0] // take only the part before ' - '
                          result[id] = trimmedName
                        }
                      })
                      setPrivilege((prev) => ({
                        ...prev,
                        formDefinitionId: option,
                        formDefinitionList: result
                      }))
                    }}
                    loading={formDefinitionLoading}
                    labelName="name"
                    totalCount={formDefinitionCount}
                    onPageChange={(e) => setLazyParams({ ...lazyParams, page: e.page })}
                    globalFilter={
                      Object.entries(privilege?.formDefinitionList || {})
                        .map(([id, name]) => ({
                          id: parseInt(id, 10),
                          name
                        }))
                        .map((item) => item.name)
                        .join(', ') || ''
                    }
                  />
                  <SelectInput
                    label="Form User Role"
                    name="formUserRole"
                    value={privilege.formUserRole}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    options={formUserRoleOptions}
                    multiple={true}
                  />
                  <SelectInput
                    label="Form Approval Stage"
                    name="formApprovalStage"
                    value={privilege.formApprovalStage}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    options={['Approval Stage 1', 'Approval Stage 2']} // Replace with actual options
                    multiple={true}
                  />
                </div>
              </>
            </ConditionalDisplay>
          </div>
        </Card>

        <Card className={clsx('w-full mx-0', styles.documentCard)}>
          <h2 className={clsx('mt-0', styles.title)}>History</h2>
          <DataTable
            value={rows}
            paginator
            rows={historyLazyParams.rows}
            totalRecords={totalCount}
            lazy
            first={historyLazyParams.first}
            onPage={onPage}
            onSort={onSort}
            sortField={historyLazyParams.sortField}
            sortOrder={historyLazyParams.sortOrder}
            style={{ cursor: 'pointer' }}
          >
            {historyColumns.map((col, index) => (
              <Column key={index} field={col.field} header={col.header} {...col} />
            ))}
          </DataTable>
        </Card>
      </div>
    </PageContainer>
  )
}

export default PrivilegesManager

export async function getServerSideProps(context) {
  const { id } = context.params
  return {
    props: {
      id: id
    }
  }
}
